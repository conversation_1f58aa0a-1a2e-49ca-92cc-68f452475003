<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HA SPORT - Ski Beyond Your Limits</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #07272E;
            color: #ffffff;
        }

        /* Header Styles */
        .header {
            background-color: #CDFF9A;
            padding: 15px 50px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1000;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #000000;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
            line-height: 1;
        }

        .nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 30px;
        }

        .nav-link {
            text-decoration: none;
            color: #000000;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #ffffff;
        }

        .icons {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .icon {
            width: 20px;
            height: 20px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .icon:hover {
            transform: scale(1.1);
        }

        /* Dropdown Styles */
        .nav-item {
            position: relative;
        }

        .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #ffffff;
            min-width: 800px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border-radius: 8px;
            padding: 30px;
            margin-top: 10px;
        }

        .dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-content {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
        }

        .dropdown-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            transition: background-color 0.3s ease;
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: #f5f5f5;
        }

        .dropdown-item img {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .dropdown-item-title {
            color: #000000;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    </style>
</head>
<body>
   
    <header class="header">
       
        <a href="#" class="logo">
            <div class="logo-text">
                <span>HA ___</span>
                <span>SPORT</span>
            </div>
        </a>

        <!-- Navigation -->
        <nav>
            <ul class="nav">
                <li><a href="#" class="nav-link">HOME</a></li>
                <li class="nav-item">
                    <a href="#" class="nav-link" id="catalog-btn">CATALOG</a>
                    <div class="dropdown" id="catalog-dropdown">
                        <div class="dropdown-content">
                            <div class="dropdown-item">
                                <img src="imgs/boots.jpg" alt="Boots" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjYwIiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Cb290czwvdGV4dD4KPC9zdmc+'" />
                                <div class="dropdown-item-title">Boots</div>
                            </div>
                            <div class="dropdown-item">
                                <img src="imgs/helmets.jpg" alt="Helmets" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjYwIiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5IZWxtZXRzPC90ZXh0Pgo8L3N2Zz4='" />
                                <div class="dropdown-item-title">Helmets</div>
                            </div>
                            <div class="dropdown-item">
                                <img src="imgs/goggles.jpg" alt="Goggles" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjYwIiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Hb2dnbGVzPC90ZXh0Pgo8L3N2Zz4='" />
                                <div class="dropdown-item-title">Goggles</div>
                            </div>
                            <div class="dropdown-item">
                                <img src="imgs/binding.jpg" alt="Binding" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjYwIiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5CaW5kaW5nPC90ZXh0Pgo8L3N2Zz4='" />
                                <div class="dropdown-item-title">Binding</div>
                            </div>
                            <div class="dropdown-item">
                                <img src="imgs/skis.jpg" alt="Skis" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjYwIiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5Ta2lzPC90ZXh0Pgo8L3N2Zz4='" />
                                <div class="dropdown-item-title">Skis</div>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="#" class="nav-link">BLOG</a></li>
                <li><a href="#" class="nav-link">SHOP</a></li>
                <li><a href="#" class="nav-link">CONTACT US</a></li>
            </ul>
        </nav>

        <!-- Icons -->
        <div class="icons">
            <!-- Search Icon -->
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
            </svg>

            <!-- Heart Icon -->
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
            </svg>

            <!-- User Icon -->
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
            </svg>

            <!-- Cart Icon -->
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="9" cy="21" r="1"></circle>
                <circle cx="20" cy="21" r="1"></circle>
                <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
            </svg>
        </div>
    </header>
    <style>
        /* Main Container Styles */
        .main-container {
            display: flex;
            min-height: 100vh;
            background-color: #07272E;
            padding-left: 100px;
            padding-right: 20px;
            padding-bottom: 80px;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 5%;
            height: calc(100vh - 70px);
            display: flex;
            flex-direction: column;
            align-items: end;
            justify-content: space-between;
            padding-top: 200px;
            padding-bottom: 80px;
            border-right: 1px solid #fff;
        }

        .sidebar-content {
            display: flex;
            flex-direction: column;
            gap: 300px;
            width: 80%;
        }

        .copyright {
            font-size: 18px;
            transform: rotate(-90deg);
            white-space: nowrap;
            color: #CDFF9A;
        }

        .social-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 70px;
        }

        .follow-text {
            font-size: 18px;
            transform: rotate(-90deg);
            white-space: nowrap;
            margin-bottom: 20px;
            color: #CDFF9A;
        }

        .social-icons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .social-icon {
            color: #ffffff;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s ease;
            text-decoration: none;
        }

        .social-icon:hover.linkedin { color: #0077B5; }
        .social-icon:hover.facebook { color: #4267B2; }
        .social-icon:hover.twitter { color: #1DA1F2; }
        .social-icon:hover.instagram { color: #E4405F; }
    </style>

    
    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-content">
                <div class="copyright">
                    2023 Dotcreativemarket
                </div>

                <div class="social-section">
                    <div class="follow-text">
                        FOLLOW US
                    </div>
                    <div class="social-icons">
                        <a href="#" class="social-icon linkedin">📧</a>
                        <a href="#" class="social-icon facebook">📘</a>
                        <a href="#" class="social-icon twitter">🐦</a>
                        <a href="#" class="social-icon instagram">📷</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">
            <div class="background-logo">
                <img src="imgs/01.png" alt="Logo" style="width: 100%" />
            </div>

            <div class="content-text">
                <div class="subtitle">
                    BEGIN BREAKING YOUR LIMIT
                </div>

                <h1 class="main-title">
                    KNOW YOUR<br />
                    LIMITS. SKI<br />
                    BEYOND THEM.
                </h1>

                <button class="cta-button">
                    VIEW MORE
                </button>
            </div>
        </div>

    <style>
        /* Content Section Styles */
        .content-section {
            width: 40%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding-left: 60px;
            position: relative;
        }

        .background-logo {
            position: absolute;
            top: 40%;
            left: 45%;
            
            transform: translate(-50%, -50%);
            z-index: 1;
        }

        .background-logo img {
            width: 900px;
            height: 213px;
            opacity: 0.3;
        }

        .content-text {
            position: relative;
            z-index: 2;
            top: 150px;
            left: 70px;
        }

        .subtitle {
            font-size: 17px;
            color: #CDFF9A;
            margin-bottom: 30px;
            letter-spacing: 2px;
            font-weight: 400;
        }

        .main-title {
            font-size: 60px;
            font-weight: 500;
            color: #ffffff;
            line-height: 1.1;
            margin-bottom: 50px;
            font-family: Arial, sans-serif;
        }

        .cta-button {
            background-color: transparent;
            border: 2px solid #CDFF9A;
            color: #CDFF9A;
            padding: 15px 30px;
            font-size: 14px;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .cta-button:hover {
            background-color: #CDFF9A;
            color: #1a3d2e;
        }
    </style>
        <!-- Image Section -->
        <div class="image-section">
            <img src="imgs/Image1.png" alt="Skier" class="main-image" />

            <div class="navigation-arrows">
                <div class="arrow-left">
                    <div class="arrow-point-left"></div>
                </div>
                <div class="arrow-right">
                    <div class="arrow-point-right"></div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Image Section Styles */
        .image-section {
            width: 50%;
            position: relative;
        }

        .main-image {
            width: 100%;
            height: auto;
        }

        .navigation-arrows {
            position: absolute;
            right: -100px;
            bottom: 90px;
            transform: translateX(-50%);
            display: flex;
            align-items: end;
            gap: 20px;
            backdrop-filter: blur(10px);
        }

        .arrow-left, .arrow-right {
            width: 100px;
            height: 2px;
            background-color: #ffffff;
            position: relative;
            cursor: pointer;
        }

        .arrow-point-left {
            position: absolute;
            left: -5px;
            top: -3px;
            width: 0;
            height: 0;
            border-right: 8px solid #ffffff;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }

        .arrow-point-right {
            position: absolute;
            right: -5px;
            top: -3px;
            width: 0;
            height: 0;
            border-left: 8px solid #ffffff;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const catalogBtn = document.getElementById('catalog-btn');
            const catalogDropdown = document.getElementById('catalog-dropdown');

            // Toggle dropdown on click
            catalogBtn.addEventListener('click', function(e) {
                e.preventDefault();
                catalogDropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!catalogBtn.contains(e.target) && !catalogDropdown.contains(e.target)) {
                    catalogDropdown.classList.remove('show');
                }
            });

            // Close dropdown when pressing Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    catalogDropdown.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>
